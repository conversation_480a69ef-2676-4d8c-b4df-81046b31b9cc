<template>
  <div class="image-generator-page">
    <div class="page-header">
      <h1>商品背景图生成管理</h1>
      <p>自动为商品生成美观的背景图片，支持根据商品类别自动选择配色方案</p>
    </div>

    <!-- 功能说明 -->
    <el-card class="info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>功能说明</span>
        </div>
      </template>
      <div class="info-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <i class="el-icon-picture" style="font-size: 24px; color: #409EFF;"></i>
              <h4>智能识别</h4>
              <p>根据商品名称自动识别商品类别，选择合适的配色方案</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <i class="el-icon-brush" style="font-size: 24px; color: #67C23A;"></i>
              <h4>自动生成</h4>
              <p>自动生成渐变背景、装饰元素和商品名称标签</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <i class="el-icon-download" style="font-size: 24px; color: #E6A23C;"></i>
              <h4>批量下载</h4>
              <p>支持单个下载或批量下载所有生成的背景图</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 图片生成器组件 -->
    <ProductImageGenerator />

    <!-- 使用说明 -->
    <el-card class="usage-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>使用说明</span>
        </div>
      </template>
      <div class="usage-content">
        <ol>
          <li><strong>调整尺寸：</strong>根据需要设置生成图片的宽度和高度</li>
          <li><strong>单个生成：</strong>点击商品卡片上的"生成背景图"按钮为单个商品生成背景图</li>
          <li><strong>批量生成：</strong>点击"生成所有商品背景图"按钮为所有商品批量生成背景图</li>
          <li><strong>预览下载：</strong>生成完成后可以预览效果，满意后下载使用</li>
          <li><strong>智能分类：</strong>系统会根据商品名称自动识别类别并应用相应的配色方案</li>
        </ol>
        
        <div class="category-colors">
          <h4>支持的商品类别及配色：</h4>
          <div class="color-samples">
            <div class="color-sample" v-for="(colors, category) in categoryColors" :key="category">
              <div class="color-preview" :style="{ background: colors.primary }"></div>
              <span>{{ category }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import ProductImageGenerator from '@/components/ProductImageGenerator.vue'

export default {
  name: 'ImageGeneratorPage',
  components: {
    ProductImageGenerator
  },
  data() {
    return {
      categoryColors: {
        '电子产品': { primary: '#2196F3' },
        '服装': { primary: '#E91E63' },
        '食品': { primary: '#FF9800' },
        '家居': { primary: '#4CAF50' },
        '美妆': { primary: '#9C27B0' },
        '运动': { primary: '#FF5722' },
        '图书': { primary: '#795548' },
        '玩具': { primary: '#FFEB3B' },
        '默认': { primary: '#607D8B' }
      }
    }
  }
}
</script>

<style scoped>
.image-generator-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 16px;
}

.info-card,
.usage-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.info-content {
  padding: 20px 0;
}

.info-item {
  text-align: center;
  padding: 20px;
}

.info-item h4 {
  margin: 10px 0;
  color: #333;
}

.info-item p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.usage-content {
  padding: 20px 0;
}

.usage-content ol {
  margin-bottom: 20px;
}

.usage-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.category-colors {
  margin-top: 20px;
}

.category-colors h4 {
  margin-bottom: 15px;
  color: #333;
}

.color-samples {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.color-sample {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
}
</style>
