<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>商品背景图生成器演示</h1>
      <p>这个工具可以为您的商品自动生成美观的背景图片</p>
    </div>

    <!-- 快速演示 -->
    <el-card class="demo-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>快速演示</span>
          <el-button type="primary" @click="generateDemo">生成演示图片</el-button>
        </div>
      </template>
      
      <div class="demo-content">
        <div class="demo-input">
          <el-form inline>
            <el-form-item label="商品名称:">
              <el-input v-model="demoProduct.name" placeholder="请输入商品名称"></el-input>
            </el-form-item>
            <el-form-item label="商品类别:">
              <el-select v-model="demoProduct.category" placeholder="选择类别">
                <el-option label="电子产品" value="电子产品"></el-option>
                <el-option label="服装" value="服装"></el-option>
                <el-option label="食品" value="食品"></el-option>
                <el-option label="家居" value="家居"></el-option>
                <el-option label="美妆" value="美妆"></el-option>
                <el-option label="运动" value="运动"></el-option>
                <el-option label="图书" value="图书"></el-option>
                <el-option label="玩具" value="玩具"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        
        <div v-if="demoImage" class="demo-result">
          <h3>生成结果:</h3>
          <div class="demo-image-container">
            <img :src="demoImage" alt="生成的背景图" />
            <div class="demo-actions">
              <el-button @click="downloadDemo">下载图片</el-button>
              <el-button @click="regenerateDemo">重新生成</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 功能特点 -->
    <el-card class="features-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>功能特点</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="feature-item">
            <div class="feature-icon">🎨</div>
            <h4>智能配色</h4>
            <p>根据商品类别自动选择合适的配色方案，让每个商品都有独特的视觉效果</p>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="feature-item">
            <div class="feature-icon">⚡</div>
            <h4>快速生成</h4>
            <p>使用Canvas技术实时生成，无需等待，支持批量处理多个商品</p>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="feature-item">
            <div class="feature-icon">📱</div>
            <h4>多尺寸支持</h4>
            <p>支持自定义图片尺寸，适配不同的展示场景和设备</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 使用步骤 -->
    <el-card class="steps-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>使用步骤</span>
        </div>
      </template>
      
      <el-steps :active="4" finish-status="success">
        <el-step title="准备商品数据" description="确保商品有名称和类别信息"></el-step>
        <el-step title="设置图片尺寸" description="根据需要调整生成图片的宽度和高度"></el-step>
        <el-step title="生成背景图" description="单个生成或批量生成所有商品背景图"></el-step>
        <el-step title="下载使用" description="预览效果后下载图片用于商品展示"></el-step>
      </el-steps>
    </el-card>

    <!-- 开始使用按钮 -->
    <div class="start-section">
      <el-button type="primary" size="large" @click="goToGenerator">
        开始使用图片生成器
      </el-button>
    </div>
  </div>
</template>

<script>
import ImageGenerator from '@/utils/imageGenerator.js'
import { ElMessage } from 'element-plus'

export default {
  name: 'ImageGeneratorDemo',
  data() {
    return {
      imageGenerator: new ImageGenerator(),
      demoProduct: {
        name: 'iPhone 15 Pro',
        category: '电子产品'
      },
      demoImage: null
    }
  },
  
  methods: {
    generateDemo() {
      if (!this.demoProduct.name) {
        ElMessage.warning('请输入商品名称')
        return
      }
      
      try {
        this.demoImage = this.imageGenerator.generateProductBackground(
          this.demoProduct,
          400,
          300
        )
        ElMessage.success('演示图片生成成功！')
      } catch (error) {
        console.error('生成演示图片失败:', error)
        ElMessage.error('生成失败，请重试')
      }
    },
    
    regenerateDemo() {
      this.generateDemo()
    },
    
    downloadDemo() {
      if (this.demoImage) {
        this.imageGenerator.downloadImage(this.demoImage, `${this.demoProduct.name}_demo.png`)
        ElMessage.success('图片下载成功！')
      }
    },
    
    goToGenerator() {
      this.$router.push('/manage/imageGenerator')
    }
  },
  
  mounted() {
    // 自动生成一个演示图片
    this.generateDemo()
  }
}
</script>

<style scoped>
.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 16px;
}

.demo-card,
.features-card,
.steps-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.demo-content {
  padding: 20px 0;
}

.demo-input {
  margin-bottom: 20px;
}

.demo-result {
  text-align: center;
}

.demo-image-container {
  display: inline-block;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.demo-image-container img {
  display: block;
  width: 400px;
  height: 300px;
}

.demo-actions {
  padding: 15px;
  background: #f9f9f9;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.feature-item {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.feature-item h4 {
  margin: 10px 0;
  color: #333;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
}

.start-section {
  text-align: center;
  margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-container {
    padding: 10px;
  }
  
  .demo-image-container img {
    width: 100%;
    max-width: 400px;
    height: auto;
  }
}
</style>
