import { createRouter, createWebHistory } from 'vue-router'
//import request from '../utils/request';


//requireAuth: 是否需要检查登录
const routes = [
     //前台
  {
    path: '/',
    name: 'front',
    redirect: "/IndexView",
    component: () => import('../views/front/Front.vue'),
    meta: {title:'在线商城', path: '在线商城', requireAuth: false},
    children: [
      {path: 'IndexView', name: 'IndexView', meta: {title:'在线商城'}, component: () => import('../views/front/IndexView.vue'),},
    ]
  },

  
  // 登录&注册 
  {
    path: '/login',
    name: 'LoginView',
    meta: {
      title: '登录',
      requireAuth: false,
    },
    component: () => import(/* webpackChunkName: "about" */ '../views/Login.vue')
  },

  {
    path: '/*',
    name: 'NotFound',
    meta: {
      title: '找不到页面'
    },
    component: () => import(/* webpackChunkName: "about" */ '../views/404NotFound.vue')
  },
]
// 创建路由器
const router = createRouter({
  history: createWebHistory(), // 替代 mode: 'history'
  routes
})


//beforeEach是router的钩子函数，在进入路由前执行
// router.beforeEach((to, from, next) => {
//   // if(to.path === '/manage'){
//   //   let user = localStorage.getItem("user");
//   //   if(!user.token){
//   //     next('/login');
//   //   }
//   // }
//   let role;
//   let allow = false;
//   if(to.meta.requireAuth===true){
//     //在后台获得该用户的身份
//     request.post("http://localhost:9197/role").then(res=>{
//       if(res.code==='200'){
//         role = res.data;
//         console.log('您的身份是：'+role);
//         if(role === 'admin'){
//           allow = true;
//         }
//         else if(role==='user'){
//             alert("您没有权限");
//             allow = false;
//             next("/")
//         }
//       }
//       else{  //查询身份失败
//         alert(res.msg);
//         next('/login');
//       }
//       //放行
//       if(allow === true){
//         //设置网页title
//         if (to.meta.title) {
//           document.title = to.meta.title
//         } else {
//           document.title ='未知页面'
//         }
//         next()
//       }
//     }
//     )
//   }
//   else{    //不需要判断权限
//     if(to.meta.requireLogin===true){
//       if(!isLogin()){
//         next('/login');
//       }
//     }
//     if (to.meta.title) {
//       document.title = to.meta.title
//     } else {
//       document.title ='未知页面'
//     }
//     next()
//   }

// })

// function isLogin() {
//   let user = localStorage.getItem("user");
//   if(user){
//     return true;
//   }else{
//     return false;
//   }
// }
/*import 'vue-vibe'*/
export default router
