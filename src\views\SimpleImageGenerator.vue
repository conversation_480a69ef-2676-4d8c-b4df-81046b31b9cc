<template>
  <div class="simple-generator">
    <div class="header">
      <h2>商品背景图生成器</h2>
      <p>为您的商品自动生成美观的背景图片</p>
    </div>

    <!-- 快速生成区域 -->
    <div class="generator-section">
      <div class="input-section">
        <div class="form-group">
          <label>商品名称:</label>
          <input v-model="productName" placeholder="请输入商品名称" />
        </div>
        
        <div class="form-group">
          <label>商品类别:</label>
          <select v-model="productCategory">
            <option value="">自动识别</option>
            <option value="电子产品">电子产品</option>
            <option value="服装">服装</option>
            <option value="食品">食品</option>
            <option value="家居">家居</option>
            <option value="美妆">美妆</option>
            <option value="运动">运动</option>
            <option value="图书">图书</option>
            <option value="玩具">玩具</option>
          </select>
        </div>

        <div class="form-group">
          <label>图片尺寸:</label>
          <input v-model.number="imageWidth" type="number" min="200" max="800" style="width: 80px" />
          x
          <input v-model.number="imageHeight" type="number" min="150" max="600" style="width: 80px" />
        </div>

        <button @click="generateImage" :disabled="!productName" class="generate-btn">
          生成背景图
        </button>
      </div>

      <div class="preview-section">
        <div v-if="generatedImage" class="image-preview">
          <h3>生成结果:</h3>
          <div class="image-container">
            <img :src="generatedImage" :alt="productName" />
            <div class="image-actions">
              <button @click="downloadImage" class="download-btn">下载图片</button>
              <button @click="regenerateImage" class="regenerate-btn">重新生成</button>
            </div>
          </div>
        </div>
        <div v-else class="placeholder">
          <p>请输入商品名称并点击生成按钮</p>
        </div>
      </div>
    </div>

    <!-- 示例展示 -->
    <div class="examples-section">
      <h3>示例效果</h3>
      <div class="examples-grid">
        <div v-for="example in examples" :key="example.name" class="example-item">
          <div class="example-image">
            <canvas :ref="`canvas-${example.name}`" width="200" height="150"></canvas>
          </div>
          <p>{{ example.name }} ({{ example.category }})</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageGenerator from '@/utils/imageGenerator.js'

export default {
  name: 'SimpleImageGenerator',
  data() {
    return {
      imageGenerator: new ImageGenerator(),
      productName: 'iPhone 15 Pro',
      productCategory: '',
      imageWidth: 400,
      imageHeight: 300,
      generatedImage: null,
      examples: [
        { name: 'iPhone 15', category: '电子产品' },
        { name: '连衣裙', category: '服装' },
        { name: '咖啡豆', category: '食品' },
        { name: '沙发', category: '家居' }
      ]
    }
  },

  mounted() {
    this.generateExamples()
    this.generateImage()
  },

  methods: {
    generateImage() {
      if (!this.productName.trim()) {
        alert('请输入商品名称')
        return
      }

      try {
        const product = {
          name: this.productName,
          category: this.productCategory
        }

        this.generatedImage = this.imageGenerator.generateProductBackground(
          product,
          this.imageWidth,
          this.imageHeight
        )
      } catch (error) {
        console.error('生成图片失败:', error)
        alert('生成失败，请重试')
      }
    },

    regenerateImage() {
      this.generateImage()
    },

    downloadImage() {
      if (this.generatedImage) {
        this.imageGenerator.downloadImage(
          this.generatedImage,
          `${this.productName}_background.png`
        )
      }
    },

    generateExamples() {
      this.$nextTick(() => {
        this.examples.forEach(example => {
          const canvas = this.$refs[`canvas-${example.name}`]
          if (canvas && canvas[0]) {
            const ctx = canvas[0].getContext('2d')
            const imageData = this.imageGenerator.generateProductBackground(
              example,
              200,
              150
            )
            
            const img = new Image()
            img.onload = () => {
              ctx.drawImage(img, 0, 0, 200, 150)
            }
            img.src = imageData
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.simple-generator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.generator-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.input-section {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.generate-btn {
  background: #409EFF;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  width: 100%;
}

.generate-btn:hover {
  background: #337ecc;
}

.generate-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.preview-section {
  padding: 20px;
  text-align: center;
}

.image-preview h3 {
  margin-bottom: 15px;
  color: #333;
}

.image-container {
  display: inline-block;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-container img {
  display: block;
  max-width: 100%;
  height: auto;
}

.image-actions {
  padding: 15px;
  background: #f9f9f9;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.download-btn,
.regenerate-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.download-btn {
  background: #67C23A;
  color: white;
}

.download-btn:hover {
  background: #5daf34;
}

.regenerate-btn {
  background: #E6A23C;
  color: white;
}

.regenerate-btn:hover {
  background: #cf9236;
}

.placeholder {
  padding: 60px 20px;
  color: #999;
  background: #f9f9f9;
  border-radius: 8px;
  border: 2px dashed #ddd;
}

.examples-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.examples-section h3 {
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.example-item {
  text-align: center;
}

.example-image {
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.example-item p {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .generator-section {
    grid-template-columns: 1fr;
  }
  
  .examples-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 480px) {
  .examples-grid {
    grid-template-columns: 1fr;
  }
}
</style>
