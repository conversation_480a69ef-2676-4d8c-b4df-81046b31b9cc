# 商品背景图自动生成器

## 功能介绍

商品背景图自动生成器是一个基于Canvas技术的前端工具，可以为电商商品自动生成美观的背景图片。该工具具有以下特点：

- 🎨 **智能配色**：根据商品类别自动选择合适的配色方案
- ⚡ **快速生成**：实时生成，无需等待，支持批量处理
- 📱 **多尺寸支持**：支持自定义图片尺寸，适配不同场景
- 🛍️ **类别识别**：智能识别商品类别，应用相应的视觉风格

## 支持的商品类别

| 类别 | 主色调 | 图标 | 适用商品 |
|------|--------|------|----------|
| 电子产品 | 蓝色系 | 📱 | 手机、电脑、数码产品等 |
| 服装 | 粉色系 | 👕 | 衣服、鞋子、配饰等 |
| 食品 | 橙色系 | 🍎 | 食品、饮料、零食等 |
| 家居 | 绿色系 | 🏠 | 家具、装饰、厨具等 |
| 美妆 | 紫色系 | 💄 | 化妆品、护肤品等 |
| 运动 | 红色系 | ⚽ | 运动用品、健身器材等 |
| 图书 | 棕色系 | 📚 | 书籍、文具等 |
| 玩具 | 黄色系 | 🧸 | 玩具、游戏等 |

## 使用方法

### 1. 访问演示页面
访问 `/demo` 路径查看功能演示和快速体验。

### 2. 管理后台使用
1. 登录管理后台
2. 进入 "文件管理" -> "商品背景图生成"
3. 设置图片尺寸（默认400x300）
4. 选择生成方式：
   - 单个生成：点击商品卡片上的"生成背景图"按钮
   - 批量生成：点击"生成所有商品背景图"按钮
5. 预览生成效果
6. 下载图片使用

### 3. 编程方式使用

```javascript
import ImageGenerator from '@/utils/imageGenerator.js'

// 创建生成器实例
const generator = new ImageGenerator()

// 生成单个商品背景图
const product = {
  name: 'iPhone 15 Pro',
  category: '电子产品' // 可选，会自动推断
}

const backgroundImage = generator.generateProductBackground(product, 400, 300)

// 批量生成
const products = [
  { name: 'iPhone 15 Pro', category: '电子产品' },
  { name: '连衣裙', category: '服装' },
  // ...更多商品
]

const results = generator.batchGenerateBackgrounds(products, 400, 300)

// 下载图片
generator.downloadImage(backgroundImage, 'product_background.png')
```

## API 文档

### ImageGenerator 类

#### 构造函数
```javascript
const generator = new ImageGenerator()
```

#### 方法

##### generateProductBackground(product, width, height)
生成单个商品背景图

**参数：**
- `product` (Object): 商品对象
  - `name` (string): 商品名称
  - `category` (string, 可选): 商品类别
- `width` (number, 默认400): 图片宽度
- `height` (number, 默认300): 图片高度

**返回：** base64格式的图片数据

##### batchGenerateBackgrounds(products, width, height)
批量生成商品背景图

**参数：**
- `products` (Array): 商品数组
- `width` (number, 默认400): 图片宽度  
- `height` (number, 默认300): 图片高度

**返回：** 包含商品ID和生成图片的数组

##### inferCategory(productName)
根据商品名称推断类别

**参数：**
- `productName` (string): 商品名称

**返回：** 推断的商品类别

##### downloadImage(dataUrl, filename)
下载生成的图片

**参数：**
- `dataUrl` (string): base64图片数据
- `filename` (string): 文件名

## 技术实现

### 核心技术
- **Canvas API**：用于图形绘制和图片生成
- **Vue 3**：前端框架
- **Element Plus**：UI组件库

### 生成流程
1. 根据商品名称推断类别（如果未提供）
2. 选择对应的配色方案和图标
3. 创建Canvas画布
4. 绘制渐变背景
5. 添加装饰性元素（圆圈、图标）
6. 绘制商品名称标签
7. 添加边框装饰
8. 导出为base64格式图片

### 配色方案设计
每个商品类别都有专门设计的配色方案：
- **主色调**：用于主要元素和边框
- **次要色**：用于背景渐变
- **强调色**：用于重要元素突出

## 自定义扩展

### 添加新的商品类别
在 `imageGenerator.js` 中的 `categoryColors` 和 `categoryIcons` 对象中添加新类别：

```javascript
this.categoryColors = {
  // 现有类别...
  '新类别': {
    primary: '#主色调',
    secondary: '#次要色',
    accent: '#强调色'
  }
}

this.categoryIcons = {
  // 现有图标...
  '新类别': '🆕'
}
```

### 修改识别规则
在 `inferCategory` 方法中添加新的关键词识别规则：

```javascript
// 新类别关键词
if (name.includes('关键词1') || name.includes('关键词2')) {
  return '新类别'
}
```

## 注意事项

1. **浏览器兼容性**：需要支持Canvas API的现代浏览器
2. **性能考虑**：批量生成大量图片时可能影响页面性能
3. **图片质量**：生成的是位图格式，放大可能失真
4. **中文字体**：确保系统支持中文字体显示

## 更新日志

### v1.0.0 (2025-01-02)
- 初始版本发布
- 支持8种商品类别
- 实现智能类别识别
- 支持单个和批量生成
- 提供完整的管理界面
