<template>
  <div class="image-generator-container">
    <div class="header">
      <h2>商品背景图自动生成器</h2>
      <div class="controls">
        <el-button type="primary" @click="generateAllImages" :loading="generating">
          生成所有商品背景图
        </el-button>
        <el-button @click="downloadAllImages" :disabled="generatedImages.length === 0">
          下载所有图片
        </el-button>
        <el-button @click="clearGenerated">清空生成结果</el-button>
      </div>
    </div>

    <!-- 尺寸设置 -->
    <div class="size-controls">
      <el-form inline>
        <el-form-item label="图片宽度:">
          <el-input-number v-model="imageWidth" :min="200" :max="800" :step="50"></el-input-number>
        </el-form-item>
        <el-form-item label="图片高度:">
          <el-input-number v-model="imageHeight" :min="150" :max="600" :step="50"></el-input-number>
        </el-form-item>
      </el-form>
    </div>

    <!-- 商品列表和生成结果 -->
    <div class="content">
      <!-- 原始商品列表 -->
      <div class="products-section">
        <h3>商品列表 ({{ products.length }})</h3>
        <div class="products-grid">
          <div 
            v-for="product in products" 
            :key="product.id" 
            class="product-card"
          >
            <div class="product-image">
              <img 
                v-if="product.imgs" 
                :src="getImageUrl(product.imgs)" 
                :alt="product.name"
                @error="handleImageError"
              />
              <div v-else class="no-image">无图片</div>
            </div>
            <div class="product-info">
              <h4>{{ product.name }}</h4>
              <p class="price">¥{{ product.price }}</p>
              <p class="category">类别: {{ inferCategory(product.name) }}</p>
              <el-button 
                size="small" 
                type="primary" 
                @click="generateSingleImage(product)"
                :loading="generatingIds.includes(product.id)"
              >
                生成背景图
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 生成的背景图 -->
      <div v-if="generatedImages.length > 0" class="generated-section">
        <h3>生成的背景图 ({{ generatedImages.length }})</h3>
        <div class="generated-grid">
          <div 
            v-for="item in generatedImages" 
            :key="item.id" 
            class="generated-card"
          >
            <div class="generated-image">
              <img :src="item.generatedBackground" :alt="item.name" />
            </div>
            <div class="generated-info">
              <h4>{{ item.name }}</h4>
              <div class="actions">
                <el-button 
                  size="small" 
                  @click="downloadSingleImage(item)"
                >
                  下载
                </el-button>
                <el-button 
                  size="small" 
                  type="success"
                  @click="previewImage(item.generatedBackground)"
                >
                  预览
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="50%">
      <div class="preview-container">
        <img :src="previewImageUrl" alt="预览图片" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImageGenerator from '@/utils/imageGenerator.js'
import request from '@/utils/request.js'
import { ElMessage } from 'element-plus'

export default {
  name: 'ProductImageGenerator',
  data() {
    return {
      imageGenerator: new ImageGenerator(),
      products: [],
      generatedImages: [],
      generating: false,
      generatingIds: [],
      imageWidth: 400,
      imageHeight: 300,
      previewVisible: false,
      previewImageUrl: '',
      baseApi: 'http://localhost:9197'
    }
  },
  
  created() {
    this.loadProducts()
  },

  methods: {
    // 加载商品数据
    async loadProducts() {
      try {
        const response = await request.get('/good/findFrontGoods')
        this.products = Array.isArray(response) ? response : []
        ElMessage.success(`加载了 ${this.products.length} 个商品`)
      } catch (error) {
        console.error('加载商品失败:', error)
        ElMessage.error('加载商品数据失败')
        this.products = []
      }
    },

    // 推断商品类别
    inferCategory(productName) {
      return this.imageGenerator.inferCategory(productName)
    },

    // 获取图片URL
    getImageUrl(imgPath) {
      if (!imgPath) return ''
      return imgPath.startsWith('http') ? imgPath : this.baseApi + imgPath
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9veWbvueJhzwvdGV4dD48L3N2Zz4='
    },

    // 生成单个商品背景图
    async generateSingleImage(product) {
      this.generatingIds.push(product.id)
      
      try {
        const generatedBackground = this.imageGenerator.generateProductBackground(
          product, 
          this.imageWidth, 
          this.imageHeight
        )
        
        // 检查是否已存在，如果存在则更新，否则添加
        const existingIndex = this.generatedImages.findIndex(item => item.id === product.id)
        const newItem = {
          id: product.id,
          name: product.name,
          originalImage: product.imgs,
          generatedBackground: generatedBackground
        }
        
        if (existingIndex >= 0) {
          this.generatedImages.splice(existingIndex, 1, newItem)
        } else {
          this.generatedImages.push(newItem)
        }
        
        ElMessage.success(`已生成 ${product.name} 的背景图`)
      } catch (error) {
        console.error('生成背景图失败:', error)
        ElMessage.error(`生成 ${product.name} 背景图失败`)
      } finally {
        const index = this.generatingIds.indexOf(product.id)
        if (index > -1) {
          this.generatingIds.splice(index, 1)
        }
      }
    },

    // 生成所有商品背景图
    async generateAllImages() {
      if (this.products.length === 0) {
        ElMessage.warning('没有商品数据')
        return
      }

      this.generating = true
      this.generatedImages = []

      try {
        const results = this.imageGenerator.batchGenerateBackgrounds(
          this.products, 
          this.imageWidth, 
          this.imageHeight
        )
        this.generatedImages = results
        ElMessage.success(`成功生成 ${results.length} 个商品背景图`)
      } catch (error) {
        console.error('批量生成失败:', error)
        ElMessage.error('批量生成背景图失败')
      } finally {
        this.generating = false
      }
    },

    // 下载单个图片
    downloadSingleImage(item) {
      const filename = `${item.name}_background.png`
      this.imageGenerator.downloadImage(item.generatedBackground, filename)
      ElMessage.success(`已下载 ${item.name} 的背景图`)
    },

    // 下载所有图片
    downloadAllImages() {
      this.generatedImages.forEach(item => {
        const filename = `${item.name}_background.png`
        this.imageGenerator.downloadImage(item.generatedBackground, filename)
      })
      ElMessage.success(`已下载 ${this.generatedImages.length} 个背景图`)
    },

    // 预览图片
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl
      this.previewVisible = true
    },

    // 清空生成结果
    clearGenerated() {
      this.generatedImages = []
      ElMessage.info('已清空生成结果')
    }
  }
}
</script>

<style scoped>
.image-generator-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #eee;
}

.header h2 {
  margin: 0;
  color: #333;
}

.controls {
  display: flex;
  gap: 10px;
}

.size-controls {
  margin-bottom: 20px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.products-section h3,
.generated-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.products-grid,
.generated-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.product-card,
.generated-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.product-card:hover,
.generated-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.product-image,
.generated-image {
  height: 150px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.product-image img,
.generated-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #999;
  font-size: 14px;
}

.product-info,
.generated-info {
  padding: 15px;
}

.product-info h4,
.generated-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.price {
  color: #e60000;
  font-weight: bold;
  margin: 5px 0;
}

.category {
  color: #666;
  font-size: 12px;
  margin: 5px 0;
}

.actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.preview-container {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
  }
  
  .controls {
    flex-wrap: wrap;
  }
  
  .products-grid,
  .generated-grid {
    grid-template-columns: 1fr;
  }
}
</style>
