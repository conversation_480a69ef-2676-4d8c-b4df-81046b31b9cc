// 商品背景图自动生成工具
// 根据商品类别和名称自动生成相应的背景图

class ImageGenerator {
  constructor() {
    // 商品类别对应的颜色主题
    this.categoryColors = {
      '电子产品': {
        primary: '#2196F3',
        secondary: '#E3F2FD',
        accent: '#1976D2'
      },
      '服装': {
        primary: '#E91E63',
        secondary: '#FCE4EC',
        accent: '#C2185B'
      },
      '食品': {
        primary: '#FF9800',
        secondary: '#FFF3E0',
        accent: '#F57C00'
      },
      '家居': {
        primary: '#4CAF50',
        secondary: '#E8F5E8',
        accent: '#388E3C'
      },
      '美妆': {
        primary: '#9C27B0',
        secondary: '#F3E5F5',
        accent: '#7B1FA2'
      },
      '运动': {
        primary: '#FF5722',
        secondary: '#FBE9E7',
        accent: '#D84315'
      },
      '图书': {
        primary: '#795548',
        secondary: '#EFEBE9',
        accent: '#5D4037'
      },
      '玩具': {
        primary: '#FFEB3B',
        secondary: '#FFFDE7',
        accent: '#F57F17'
      },
      '默认': {
        primary: '#607D8B',
        secondary: '#ECEFF1',
        accent: '#455A64'
      }
    }

    // 商品类别对应的图标
    this.categoryIcons = {
      '电子产品': '📱',
      '服装': '👕',
      '食品': '🍎',
      '家居': '🏠',
      '美妆': '💄',
      '运动': '⚽',
      '图书': '📚',
      '玩具': '🧸',
      '默认': '🛍️'
    }
  }

  /**
   * 根据商品名称推断商品类别
   * @param {string} productName - 商品名称
   * @returns {string} - 商品类别
   */
  inferCategory(productName) {
    const name = productName.toLowerCase()
    
    // 电子产品关键词
    if (name.includes('手机') || name.includes('电脑') || name.includes('平板') || 
        name.includes('耳机') || name.includes('音响') || name.includes('相机') ||
        name.includes('电视') || name.includes('充电') || name.includes('数码')) {
      return '电子产品'
    }
    
    // 服装关键词
    if (name.includes('衣服') || name.includes('裤子') || name.includes('裙子') ||
        name.includes('鞋子') || name.includes('帽子') || name.includes('包包') ||
        name.includes('外套') || name.includes('t恤') || name.includes('牛仔')) {
      return '服装'
    }
    
    // 食品关键词
    if (name.includes('食品') || name.includes('零食') || name.includes('饮料') ||
        name.includes('水果') || name.includes('蔬菜') || name.includes('肉类') ||
        name.includes('奶制品') || name.includes('茶') || name.includes('咖啡')) {
      return '食品'
    }
    
    // 家居关键词
    if (name.includes('家具') || name.includes('床') || name.includes('沙发') ||
        name.includes('桌子') || name.includes('椅子') || name.includes('灯具') ||
        name.includes('装饰') || name.includes('收纳') || name.includes('厨具')) {
      return '家居'
    }
    
    // 美妆关键词
    if (name.includes('化妆品') || name.includes('护肤') || name.includes('口红') ||
        name.includes('面膜') || name.includes('香水') || name.includes('洗发') ||
        name.includes('护发') || name.includes('美容')) {
      return '美妆'
    }
    
    // 运动关键词
    if (name.includes('运动') || name.includes('健身') || name.includes('球') ||
        name.includes('跑步') || name.includes('游泳') || name.includes('瑜伽') ||
        name.includes('户外') || name.includes('登山')) {
      return '运动'
    }
    
    // 图书关键词
    if (name.includes('书') || name.includes('小说') || name.includes('教材') ||
        name.includes('杂志') || name.includes('漫画') || name.includes('文具')) {
      return '图书'
    }
    
    // 玩具关键词
    if (name.includes('玩具') || name.includes('积木') || name.includes('娃娃') ||
        name.includes('模型') || name.includes('游戏') || name.includes('益智')) {
      return '玩具'
    }
    
    return '默认'
  }

  /**
   * 生成商品背景图
   * @param {Object} product - 商品对象
   * @param {string} product.name - 商品名称
   * @param {string} product.category - 商品类别（可选）
   * @param {number} width - 图片宽度，默认400
   * @param {number} height - 图片高度，默认300
   * @returns {string} - base64格式的图片数据
   */
  generateProductBackground(product, width = 400, height = 300) {
    const category = product.category || this.inferCategory(product.name)
    const colors = this.categoryColors[category] || this.categoryColors['默认']
    const icon = this.categoryIcons[category] || this.categoryIcons['默认']
    
    // 创建canvas
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    const ctx = canvas.getContext('2d')
    
    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, width, height)
    gradient.addColorStop(0, colors.secondary)
    gradient.addColorStop(1, colors.primary + '20') // 添加透明度
    
    // 绘制背景
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)
    
    // 绘制装饰性圆圈
    this.drawDecorativeCircles(ctx, width, height, colors)
    
    // 绘制类别图标
    ctx.font = `${Math.min(width, height) * 0.3}px Arial`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillStyle = colors.primary + '30'
    ctx.fillText(icon, width * 0.7, height * 0.3)
    
    // 绘制商品名称
    this.drawProductName(ctx, product.name, width, height, colors)
    
    // 绘制边框
    ctx.strokeStyle = colors.accent
    ctx.lineWidth = 2
    ctx.strokeRect(1, 1, width - 2, height - 2)
    
    return canvas.toDataURL('image/png')
  }

  /**
   * 绘制装饰性圆圈
   */
  drawDecorativeCircles(ctx, width, height, colors) {
    // 大圆圈
    ctx.beginPath()
    ctx.arc(width * 0.2, height * 0.8, Math.min(width, height) * 0.15, 0, 2 * Math.PI)
    ctx.fillStyle = colors.primary + '20'
    ctx.fill()
    
    // 小圆圈
    ctx.beginPath()
    ctx.arc(width * 0.8, height * 0.2, Math.min(width, height) * 0.08, 0, 2 * Math.PI)
    ctx.fillStyle = colors.accent + '30'
    ctx.fill()
  }

  /**
   * 绘制商品名称
   */
  drawProductName(ctx, name, width, height, colors) {
    // 截取商品名称，避免过长
    const displayName = name.length > 8 ? name.substring(0, 8) + '...' : name
    
    // 绘制文字背景
    ctx.font = '16px Arial'
    const textMetrics = ctx.measureText(displayName)
    const textWidth = textMetrics.width
    const textHeight = 30
    const textX = (width - textWidth) / 2
    const textY = height * 0.7
    
    ctx.fillStyle = colors.primary + '80'
    ctx.fillRect(textX - 10, textY - textHeight/2, textWidth + 20, textHeight)
    
    // 绘制文字
    ctx.fillStyle = '#FFFFFF'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(displayName, width / 2, textY)
  }

  /**
   * 批量生成商品背景图
   * @param {Array} products - 商品数组
   * @param {number} width - 图片宽度
   * @param {number} height - 图片高度
   * @returns {Array} - 包含商品ID和生成图片的数组
   */
  batchGenerateBackgrounds(products, width = 400, height = 300) {
    return products.map(product => ({
      id: product.id,
      name: product.name,
      originalImage: product.imgs,
      generatedBackground: this.generateProductBackground(product, width, height)
    }))
  }

  /**
   * 下载生成的图片
   * @param {string} dataUrl - base64图片数据
   * @param {string} filename - 文件名
   */
  downloadImage(dataUrl, filename) {
    const link = document.createElement('a')
    link.download = filename
    link.href = dataUrl
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

export default ImageGenerator
