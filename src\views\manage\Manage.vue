<template>
  <el-container style="height: 100vh;">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" style="background-color: rgb(48,65,86);">
      <Aside :isCollapse="isCollapse" />
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container>
      <!-- 头部 -->
      <el-header style="background-color: white; border-bottom: 1px solid #e6e6e6;">
        <Header @collapse="handleCollapse" :routePath="routePath" :user="user" />
      </el-header>
      
      <!-- 主体内容 -->
      <el-main style="background-color: #f0f2f5; padding: 20px;">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import Aside from '@/components/Aside.vue'
import Header from '@/components/Header.vue'

export default {
  name: 'ManageLayout',
  components: {
    Aside,
    Header
  },
  data() {
    return {
      isCollapse: false,
      user: {
        nickname: '管理员',
        avatarUrl: '/default-avatar.png'
      }
    }
  },
  computed: {
    routePath() {
      // 根据当前路由返回面包屑路径
      const routeMap = {
        '/manage/home': '首页',
        '/manage/imageGenerator': '商品背景图生成',
        '/manage/good': '商品管理',
        '/manage/category': '商品分类管理',
        '/manage/carousel': '轮播图管理',
        '/manage/order': '订单管理',
        '/manage/user': '用户管理',
        '/manage/file': '文件管理',
        '/manage/avatar': '头像管理',
        '/manage/incomeChart': '图表分析',
        '/manage/incomeRank': '收入排行榜'
      }
      return routeMap[this.$route.path] || '管理页面'
    }
  },
  methods: {
    handleCollapse() {
      this.isCollapse = !this.isCollapse
    },
    
    async loadUserInfo() {
      try {
        // 这里可以添加获取用户信息的API调用
        // const response = await request.get('/api/user/info')
        // this.user = response.data
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }
  },
  
  created() {
    this.loadUserInfo()
  }
}
</script>

<style scoped>
.el-header {
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.el-aside {
  transition: width 0.3s;
}

.el-main {
  overflow-y: auto;
}
</style>
