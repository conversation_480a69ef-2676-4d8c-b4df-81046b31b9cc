<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-26 15:27:05
-->
<template>
  <el-container style="height: 100%;width:100%;">
    <el-header style="background-color: white">
      <NavMenu :user="user"
                  :role="role"
                  :login-status="loginStatus"
      ></NavMenu>
    </el-header>


    <el-main style="background-color: lightgrey;width:100%;">

      <router-view />
    </el-main>

  </el-container>
</template>

<script>

import NavMenu from "@/components/Navagation.vue";
import request from "@/utils/request";

export default {
  name: "FrontView",
  data(){
    return{
      user:{},
      role: 'user',
      loginStatus: false,
    }
  },
  methods: {
    getUser() {
      let username = localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")).username : ""
      if (username) {
        // 从后台获取User数据
        this.request.get("/userinfo/" + username).then(res => {
          // 重新赋值后台的最新User数据
          this.user = res.data
          console.log(this.user.role)
        })
      }

    },
  },


  components:{
    NavMenu,
  },
  created() {
    if(localStorage.getItem("user")){
      request.post("http://localhost:9197/role").then(res=> {
        if (res.code === '200') {
          this.role = res.data;
          if (localStorage.getItem("user")) {
            this.user = JSON.parse(localStorage.getItem("user"));
            this.loginStatus = true;
          }
        } else {
          this.user = {nickname: '您未登录', avatarUrl: null};
          localStorage.removeItem('user')
          this.loginStatus = false;
        }
      })
    }else{
      this.user = {nickname: '您未登录', avatarUrl: null};
      this.loginStatus = false;
    }

  }
}
</script>

<style scoped>
@import "../../resource/css/search.css";
.image {
  width: 100%;
  display: block;
}
</style>